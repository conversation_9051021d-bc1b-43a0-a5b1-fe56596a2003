/**
 * Webhook Queue API Handlers
 *
 * HTTP handlers for webhook queue management endpoints including webhook
 * ingestion, processing triggers, and queue monitoring. Designed for
 * integration with fire-and-forget processing patterns.
 *
 * @fileoverview Queue management API handlers
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APWebhookPayload } from "@type";
import type { Context } from "hono";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import type {
	PlatformSource,
	QueueProcessingOptions,
	WebhookQueueInsert,
} from "@/queue/types";
import { WebhookQueueManager } from "@/queue/WebhookQueueManager";
import { logDebug, logError, logInfo } from "@/utils/logger";

/**
 * Add webhook to queue endpoint
 *
 * Accepts webhook payloads and adds them to the processing queue with
 * duplicate detection and priority assignment. Immediately triggers
 * processing via fire-and-forget pattern.
 *
 * **Endpoint:** POST /api/queue/webhook
 *
 * **Supports two request formats:**
 *
 * **1. Wrapper Format (existing):**
 * ```json
 * {
 *   "source": "cc" | "ap",
 *   "entityType": "patient" | "appointment" | "custom_field",
 *   "entityId": "string",
 *   "patientId": "string", // optional
 *   "appointmentId": "string", // optional
 *   "payload": { ... }, // webhook payload
 *   "priority": 100, // optional, default 100
 *   "maxRetries": 3 // optional, default 3
 * }
 * ```
 *
 * **2. Raw Webhook Format (new):**
 * Send the raw webhook payload directly. The endpoint will auto-detect
 * the source (AP vs CC) and extract metadata automatically.
 *
 * For CliniCore webhooks:
 * ```json
 * {
 *   "event": "EntityWasCreated",
 *   "model": "Patient",
 *   "id": 1766,
 *   "payload": { ... },
 *   "timestamp": "2025-07-25T22:44:33.278Z"
 * }
 * ```
 *
 * For AutoPatient webhooks:
 * ```json
 * {
 *   "contact_id": "KGU5VQTetxTHFAYS5GH9",
 *   "first_name": "John",
 *   "location": { ... },
 *   "workflow": { ... },
 *   // ... rest of AP webhook payload
 * }
 * ```
 *
 * **Response:**
 * ```json
 * {
 *   "success": true,
 *   "webhookId": "uuid",
 *   "status": "pending" | "duplicate_skipped",
 *   "duplicateOf": "uuid", // if duplicate
 *   "duplicateReason": "string" // if duplicate
 * }
 * ```
 *
 * @param c - Hono context object
 * @returns HTTP Response with webhook processing result
 */
export async function addWebhookToQueue(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received webhook queue request");

		// Parse and validate request body
		const body = await c.req.json();
		const webhook = validateWebhookRequest(body);

		if (!webhook) {
			return c.json(
				{
					success: false,
					error: "Invalid webhook request",
					message: "Missing required fields or invalid data format",
					timestamp,
				},
				400,
			);
		}

		// Add webhook to queue
		const queueManager = new WebhookQueueManager();
		const result = await queueManager.addWebhook(webhook);

		logInfo("Webhook added to queue", {
			webhookId: result.webhookId,
			status: result.status,
			source: webhook.source,
			entityType: webhook.entityType,
		});

		// Trigger processing immediately (fire-and-forget)
		if (result.status === "pending") {
			triggerQueueProcessing();
		}

		return c.json({
			success: true,
			webhookId: result.webhookId,
			status: result.status,
			duplicateOf: result.duplicateOf,
			duplicateReason: result.duplicateReason,
			timestamp,
		});
	} catch (error) {
		logError("Failed to add webhook to queue", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to process webhook request",
				timestamp,
			},
			500,
		);
	}
}

/**
 * Process queue endpoint
 *
 * Triggers processing of pending webhooks in the queue. Designed to be
 * called via fire-and-forget pattern for immediate processing or as a
 * manual trigger for queue processing.
 *
 * **Endpoint:** POST /api/queue/process
 *
 * **Request Body (optional):**
 * ```json
 * {
 *   "triggerId": "string", // optional, for correlation
 *   "batchSize": 3, // optional, default 3, max 3
 *   "maxProcessingTimeMs": 29000 // optional, default 29000
 * }
 * ```
 *
 * **Response:**
 * ```json
 * {
 *   "success": true,
 *   "processedCount": 2,
 *   "batchResults": [
 *     {
 *       "webhookId": "uuid",
 *       "status": "completed" | "failed" | "duplicate_skipped" | "timeout",
 *       "processingTimeMs": 150,
 *       "errorMessage": "string" // if failed
 *     }
 *   ],
 *   "retryCount": 1,
 *   "alertsCreated": 0,
 *   "timeoutsDetected": 0
 * }
 * ```
 *
 * @param c - Hono context object
 * @returns HTTP Response with batch processing results
 */
export async function processQueue(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received queue processing request");

		// Parse optional request body
		let options: QueueProcessingOptions = {};
		try {
			const body = await c.req.json();
			options = {
				batchSize: Math.min(body.batchSize || 3, 3), // Max 3 for safety
				maxProcessingTimeMs: body.maxProcessingTimeMs || 29000,
			};
		} catch {
			// No body or invalid JSON, use defaults
		}

		const queueManager = new WebhookQueueManager();

		// Detect and handle timeouts first
		const timeoutResult = await queueManager.detectTimeouts();
		logDebug("Timeout detection completed", {
			timedOutCount: timeoutResult.timedOutWebhooks.length,
			alertsCreated: timeoutResult.alertsCreated,
		});

		// Process batch
		const batchResult = await queueManager.processBatch(options);

		// Cleanup expired locks
		const cleanedLocks = await queueManager.cleanupExpiredLocks();

		logInfo("Queue processing completed", {
			processedCount: batchResult.processedCount,
			retryCount: batchResult.retryCount,
			alertsCreated: batchResult.alertsCreated,
			timeoutsDetected: timeoutResult.timedOutWebhooks.length,
			cleanedLocks,
		});

		return c.json({
			success: batchResult.success,
			processedCount: batchResult.processedCount,
			batchResults: batchResult.batchResults,
			retryCount: batchResult.retryCount,
			alertsCreated: batchResult.alertsCreated,
			timeoutsDetected: timeoutResult.timedOutWebhooks.length,
			cleanedLocks,
			timestamp,
		});
	} catch (error) {
		logError("Failed to process queue", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to process queue",
				timestamp,
			},
			500,
		);
	}
}

/**
 * Get queue statistics endpoint
 *
 * Returns current queue statistics for monitoring and debugging.
 *
 * **Endpoint:** GET /api/queue/stats
 *
 * @param c - Hono context object
 * @returns HTTP Response with queue statistics
 */
export async function getQueueStatistics(c: Context): Promise<Response> {
	try {
		const queueManager = new WebhookQueueManager();
		const stats = await queueManager.getQueueStatistics();

		return c.json({
			success: true,
			statistics: stats,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logError("Failed to get queue statistics", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to retrieve queue statistics",
			},
			500,
		);
	}
}

/**
 * Detect webhook source from payload structure
 *
 * @param payload - Raw webhook payload
 * @returns Detected source or null if unrecognizable
 */
function detectWebhookSource(payload: Record<string, unknown>): "ap" | "cc" | null {
	// CliniCore webhook detection: has event, model, id, payload, timestamp
	if (
		typeof payload.event === "string" &&
		typeof payload.model === "string" &&
		typeof payload.id === "number" &&
		payload.payload &&
		typeof payload.payload === "object" &&
		typeof payload.timestamp === "string"
	) {
		return "cc";
	}

	// AutoPatient webhook detection: has contact_id, location, workflow
	if (
		typeof payload.contact_id === "string" &&
		payload.location &&
		typeof payload.location === "object" &&
		payload.workflow &&
		typeof payload.workflow === "object"
	) {
		return "ap";
	}

	return null;
}

/**
 * Validate CliniCore webhook payload structure
 *
 * @param payload - Raw payload to validate
 * @returns True if valid CC webhook structure
 */
function isValidCCWebhook(payload: unknown): payload is CCWebhookPayload {
	if (!payload || typeof payload !== "object") {
		return false;
	}

	const data = payload as Record<string, unknown>;
	return (
		typeof data.event === "string" &&
		["EntityWasCreated", "EntityWasUpdated", "EntityWasDeleted", "AppointmentWasCreated", "AppointmentWasUpdated", "AppointmentWasDeleted"].includes(data.event) &&
		typeof data.model === "string" &&
		["Patient", "Appointment", "Invoice", "Payment", "Service"].includes(data.model) &&
		typeof data.id === "number" &&
		!!data.payload &&
		typeof data.payload === "object" &&
		typeof data.timestamp === "string"
	);
}

/**
 * Validate AutoPatient webhook payload structure
 *
 * @param payload - Raw payload to validate
 * @returns True if valid AP webhook structure
 */
function isValidAPWebhook(payload: unknown): payload is APWebhookPayload {
	if (!payload || typeof payload !== "object") {
		return false;
	}

	const data = payload as Record<string, unknown>;
	return (
		typeof data.contact_id === "string" &&
		typeof data.date_created === "string" &&
		!!data.location &&
		typeof data.location === "object" &&
		!!data.workflow &&
		typeof data.workflow === "object" &&
		!!data.contact &&
		typeof data.contact === "object" &&
		!!data.attributionSource &&
		typeof data.attributionSource === "object" &&
		!!data.customData &&
		typeof data.customData === "object"
	);
}

/**
 * Extract entity metadata from CliniCore webhook
 *
 * @param payload - CC webhook payload
 * @returns Entity metadata
 */
function extractCCMetadata(payload: Record<string, unknown>): {
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
} {
	const model = payload.model as string;
	const id = payload.id as number;

	// Map CC model to entity type
	const entityType = model.toLowerCase(); // "Patient" -> "patient", "Appointment" -> "appointment"
	const entityId = id.toString();

	// For patient webhooks, the entity is the patient
	if (model === "Patient") {
		return {
			entityType,
			entityId,
			patientId: entityId, // CC patient ID becomes our patient reference
		};
	}

	// For appointment webhooks, extract patient ID from payload if available
	if (model === "Appointment") {
		const appointmentPayload = payload.payload as Record<string, unknown>;
		const patients = appointmentPayload?.patients as number[] | undefined;
		const patientId = patients?.[0]?.toString(); // Use first patient if available

		return {
			entityType,
			entityId,
			appointmentId: entityId,
			patientId,
		};
	}

	// For other entities, use basic mapping
	return {
		entityType,
		entityId,
	};
}

/**
 * Extract entity metadata from AutoPatient webhook
 *
 * @param payload - AP webhook payload
 * @returns Entity metadata
 */
function extractAPMetadata(payload: Record<string, unknown>): {
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
} {
	const contactId = payload.contact_id as string;

	// Check if this is an appointment webhook (has calendar property)
	if (payload.calendar && typeof payload.calendar === "object") {
		const calendar = payload.calendar as Record<string, unknown>;
		const appointmentId = calendar.appointmentId as string;

		return {
			entityType: "appointment",
			entityId: appointmentId,
			appointmentId,
			patientId: contactId, // AP contact ID becomes our patient reference
		};
	}

	// Default to contact webhook
	return {
		entityType: "contact",
		entityId: contactId,
		patientId: contactId, // AP contact ID becomes our patient reference
	};
}

/**
 * Validate webhook request data
 *
 * Supports both wrapper format and raw webhook payloads.
 * Auto-detects webhook source and extracts metadata.
 *
 * @param body - Request body to validate
 * @returns Validated webhook data or null if invalid
 */
function validateWebhookRequest(body: unknown): WebhookQueueInsert | null {
	if (!body || typeof body !== "object") {
		return null;
	}

	const data = body as Record<string, unknown>;

	// Check if this is the wrapper format (has source, entityType, entityId, payload)
	if (
		typeof data.source === "string" &&
		["cc", "ap"].includes(data.source) &&
		typeof data.entityType === "string" &&
		typeof data.entityId === "string" &&
		data.payload &&
		typeof data.payload === "object"
	) {
		// Handle wrapper format (existing behavior)
		const source = data.source as PlatformSource;
		const baseData = {
			entityType: data.entityType,
			entityId: data.entityId,
			patientId: typeof data.patientId === "string" ? data.patientId : undefined,
			appointmentId:
				typeof data.appointmentId === "string" ? data.appointmentId : undefined,
			priority: typeof data.priority === "number" ? data.priority : undefined,
			maxRetries:
				typeof data.maxRetries === "number" ? data.maxRetries : undefined,
		};

		if (source === "cc") {
			return {
				source: "cc" as const,
				...baseData,
				payload: data.payload as CCWebhookPayload,
			};
		} else {
			return {
				source: "ap" as const,
				...baseData,
				payload: data.payload as APWebhookPayload,
			};
		}
	}

	// Handle raw webhook payload format
	const source = detectWebhookSource(data);
	if (!source) {
		return null;
	}

	// Validate the payload structure based on detected source
	if (source === "cc" && !isValidCCWebhook(data)) {
		return null;
	}
	if (source === "ap" && !isValidAPWebhook(data)) {
		return null;
	}

	// Extract metadata based on detected source
	let metadata: {
		entityType: string;
		entityId: string;
		patientId?: string;
		appointmentId?: string;
	};

	if (source === "cc") {
		metadata = extractCCMetadata(data);
	} else {
		metadata = extractAPMetadata(data);
	}

	// Create the webhook queue item
	const baseData = {
		...metadata,
		priority: 100, // Default priority for raw webhooks
		maxRetries: 3, // Default retry count for raw webhooks
	};

	if (source === "cc") {
		// TypeScript now knows this is a valid CCWebhookPayload due to validation above
		return {
			source: "cc" as const,
			...baseData,
			payload: data as unknown as CCWebhookPayload,
		};
	} else {
		// TypeScript now knows this is a valid APWebhookPayload due to validation above
		return {
			source: "ap" as const,
			...baseData,
			payload: data as unknown as APWebhookPayload,
		};
	}
}

/**
 * Trigger queue processing via fire-and-forget
 *
 * Uses the improved fire-and-forget queue processing function that automatically
 * extracts the base URL from the current Hono context.
 */
function triggerQueueProcessing(): void {
	try {
		// Import fire-and-forget utility dynamically to avoid circular imports
		import("@/queue/fireAndForgetIntegration")
			.then(({ fireAndForgetQueueProcessing }) => {
				fireAndForgetQueueProcessing(
					{},
					{
						trigger: "webhook_added",
						timestamp: new Date().toISOString(),
					},
				);
			})
			.catch((error) => {
				logError("Failed to trigger queue processing", error);
			});
	} catch (error) {
		logError("Failed to setup queue processing trigger", error);
	}
}
