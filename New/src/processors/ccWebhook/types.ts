/**
 * CliniCore Webhook Processing Types
 *
 * Type definitions and interfaces for CliniCore webhook event processing.
 * Provides comprehensive type safety for webhook payload handling, event
 * processing, patient synchronization, and response structures.
 *
 * @fileoverview Type definitions for CliniCore webhook processing operations
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { dbSchema } from "@database";
import type {
	GetAPContactType,
	GetCCPatientType,
	PostAPContactType,
} from "@type";

/**
 * CliniCore webhook event types
 */
export type CCWebhookEvent =
	| "EntityWasCreated"
	| "EntityWasUpdated"
	| "EntityWasDeleted";

/**
 * CliniCore webhook model types
 */
export type CCWebhookModel =
	| "Patient"
	| "Appointment"
	| "CustomField"
	| "Invoice"
	| "Payment";

/**
 * CliniCore webhook payload structure
 *
 * Represents the complete webhook payload received from CliniCore.
 * Contains event metadata and the actual entity data.
 */
export interface CCWebhookPayload {
	/** Event type that triggered the webhook */
	event: CCWebhookEvent;
	/** Model/entity type that was affected */
	model: CCWebhookModel;
	/** Entity ID in CliniCore */
	id: number;
	/** Complete entity data from CliniCore */
	payload: GetCCPatientType | Record<string, unknown>;
	/** Webhook timestamp from CliniCore */
	timestamp: string;
}

/**
 * Patient-specific webhook payload
 *
 * Type-safe wrapper for patient webhook events with proper payload typing.
 */
export interface CCPatientWebhookPayload
	extends Omit<CCWebhookPayload, "payload"> {
	model: "Patient";
	payload: GetCCPatientType;
}

/**
 * Event processing context
 *
 * Contains metadata and configuration for processing a webhook event.
 */
export interface EventProcessingContext {
	/** Webhook payload to process */
	payload: CCWebhookPayload;
	/** Processing timestamp */
	processedAt: Date;
	/** Sync buffer time in seconds */
	syncBufferTimeSec: number;
	/** Full processing configuration (for lock creation) */
	config?: Partial<WebhookProcessingConfig>;
}

/**
 * Patient lookup result
 *
 * Result of searching for an existing patient in the local database.
 */
export interface PatientLookupResult {
	/** Whether a patient record was found */
	found: boolean;
	/** Patient record from database (if found) */
	patient?: typeof dbSchema.patient.$inferSelect;
	/** Lookup method used (ccId, email, phone) */
	lookupMethod?: "ccId" | "email" | "phone" | "email_and_phone";
}

/**
 * Sync buffer check result
 *
 * Result of checking if an event should be processed based on sync buffer timing.
 */
export interface SyncBufferCheckResult {
	/** Whether the event should be processed */
	shouldProcess: boolean;
	/** Reason for the decision */
	reason: string;
	/** Time difference in seconds */
	timeDifference?: number;
	/** Buffer threshold used */
	bufferThreshold: number;
}

/**
 * Field mapping result
 *
 * Result of mapping CliniCore patient fields to AutoPatient contact fields.
 */
export interface FieldMappingResult {
	/** Mapped AutoPatient contact data */
	apContactData: Partial<PostAPContactType>;
	/** Standard fields that were mapped */
	mappedStandardFields: string[];
	/** Custom fields that need processing */
	customFieldsToProcess: Array<{
		ccFieldName: string;
		ccFieldValue: unknown;
		apFieldName?: string;
	}>;
	/** Fields that couldn't be mapped */
	unmappedFields: string[];
}

/**
 * AutoPatient API response for contact upsert
 */
export interface APContactUpsertResponse {
	/** Success status */
	success: boolean;
	/** Contact data from AP API */
	contact?: GetAPContactType;
	/** Error message if failed */
	error?: string;
	/** HTTP status code */
	statusCode?: number;
}

/**
 * Patient synchronization result
 *
 * Complete result of synchronizing a patient from CC to AP.
 */
export interface PatientSyncResult {
	/** Whether synchronization was successful */
	success: boolean;
	/** Synchronization action taken */
	action: "created" | "updated" | "skipped" | "failed";
	/** Patient record after synchronization */
	patient?: typeof dbSchema.patient.$inferSelect;
	/** AutoPatient contact data */
	apContact?: GetAPContactType;
	/** Field mapping details */
	fieldMapping?: FieldMappingResult;
	/** Error details if failed */
	error?: {
		message: string;
		type: string;
		details?: Record<string, unknown>;
	};
	/** Processing statistics */
	stats: {
		/** Processing duration in milliseconds */
		processingTimeMs: number;
		/** Number of fields mapped */
		fieldsMapped: number;
		/** Number of API calls made */
		apiCalls: number;
	};
}

/**
 * Event processing result
 *
 * Complete result of processing a CliniCore webhook event.
 */
export interface EventProcessingResult {
	/** Whether event processing was successful */
	success: boolean;
	/** Event that was processed */
	event: CCWebhookEvent;
	/** Model that was processed */
	model: CCWebhookModel;
	/** Entity ID that was processed */
	entityId: number;
	/** Patient synchronization result (for patient events) */
	patientSync?: PatientSyncResult;
	/** Processing metadata */
	metadata: {
		/** Processing start time */
		startTime: Date;
		/** Processing end time */
		endTime: Date;
		/** Total processing duration in milliseconds */
		durationMs: number;
	};
	/** Error details if processing failed */
	error?: {
		message: string;
		type: string;
		stage:
			| "validation"
			| "lookup"
			| "sync_check"
			| "mapping"
			| "api_call"
			| "database";
		details?: Record<string, unknown>;
	};
}

/**
 * Database patient insert type
 *
 * Type for inserting new patient records into the database.
 */
export type PatientInsert = typeof dbSchema.patient.$inferInsert;

/**
 * Database patient select type
 *
 * Type for patient records selected from the database.
 */
export type PatientSelect = typeof dbSchema.patient.$inferSelect;

/**
 * Webhook processing configuration
 *
 * Configuration options for webhook event processing.
 */
export interface WebhookProcessingConfig {
	/** Sync buffer time in seconds */
	syncBufferTimeSec: number;
	/** Maximum processing time in milliseconds */
	maxProcessingTimeMs: number;
	/** Whether to enable detailed logging */
	enableDetailedLogging: boolean;
	/** Whether to skip sync buffer checks (for testing) */
	skipSyncBufferCheck?: boolean;
	/** Webhook ID for lock creation (when called from queue) */
	webhookId?: string;
	/** Queue manager instance for lock creation */
	queueManager?: {
		createDuplicatePreventionLock: (options: {
			webhookId: string;
			targetPlatform: "cc" | "ap";
			patientId?: string;
			appointmentId?: string;
			lockDurationSeconds?: number;
		}) => Promise<void>;
	};
}
